# AWS Secrets Manager Integration Guide

This guide explains how to set up and use AWS Secrets Manager with the ChatAI Service for secure credential management.

## Overview

The ChatAI Service now supports AWS Secrets Manager for centralized secret management. This provides:

- ✅ Centralized secret storage in AWS
- ✅ Automatic secret rotation support
- ✅ Audit logging and access control
- ✅ Fallback to environment variables
- ✅ Local caching for performance
- ✅ Health monitoring

## Quick Setup

### 1. Install Dependencies

The AWS SDK is already included in the project:

```bash
npm install
```

### 2. Create AWS Secret

Create a secret in AWS Secrets Manager with the following JSON structure:

```json
{
  "LLAMA_CLOUD_API_KEY": "your-llama-cloud-api-key",
  "OPENROUTER_API_KEY": "your-openrouter-api-key",
  "OPENAI_API_KEY": "your-openai-api-key",
  "QDRANT_API_KEY": "your-qdrant-api-key",
  "INTERNAL_API_KEY": "chatai-internal-2024",
  "POSTGRES_HOST": "your-postgres-host",
  "POSTGRES_PORT": "5432",
  "POSTGRES_USER": "your-postgres-user",
  "POSTGRES_PASSWORD": "your-postgres-password",
  "POSTGRES_DB": "your-database-name",
  "USER_SERVICE_URL": "https://your-user-service.com",
  "QDRANT_URL": "https://your-qdrant-instance.com",
  "CHATAI_ORIGIN": "https://your-chatai-domain.com"
}
```

### 3. Configure Environment Variables

Set these environment variables for AWS configuration:

```bash
# Required: AWS Region
export AWS_REGION=us-east-1

# Required: Secret Name (default: chatai-service-secrets)
export AWS_SECRET_NAME=chatai-service-secrets

# AWS Credentials (choose one method):

# Method 1: Environment Variables
export AWS_ACCESS_KEY_ID=your-access-key-id
export AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Method 2: AWS Profile
export AWS_PROFILE=your-aws-profile

# Method 3: IAM Role (for EC2/ECS)
export AWS_IAM_ROLE_ARN=arn:aws:iam::account:role/ChatAIServiceRole

# Method 4: Web Identity Token (for EKS)
export AWS_WEB_IDENTITY_TOKEN_FILE=/var/run/secrets/eks.amazonaws.com/serviceaccount/token
export AWS_ROLE_ARN=arn:aws:iam::account:role/ChatAIServiceRole
```

### 4. Run the Service

```bash
npm start
```

The service will automatically:
- Detect AWS configuration
- Retrieve secrets from AWS Secrets Manager
- Fall back to environment variables if AWS is not configured
- Display configuration status in startup logs

## Configuration Methods

### Method 1: Environment Variables (Development)

For local development, you can use environment variables directly:

```bash
export LLAMA_CLOUD_API_KEY=your-api-key
export OPENROUTER_API_KEY=your-api-key
# ... other variables
npm start
```

### Method 2: AWS CLI Profile

```bash
aws configure --profile chatai-service
export AWS_PROFILE=chatai-service
export AWS_REGION=us-east-1
export AWS_SECRET_NAME=chatai-service-secrets
npm start
```

### Method 3: IAM Role (Production - EC2/ECS)

No additional configuration needed if running on EC2/ECS with proper IAM role attached:

```bash
export AWS_REGION=us-east-1
export AWS_SECRET_NAME=chatai-service-secrets
npm start
```

### Method 4: Kubernetes/EKS with IRSA

```yaml
# In your Kubernetes deployment
env:
  - name: AWS_REGION
    value: "us-east-1"
  - name: AWS_SECRET_NAME
    value: "chatai-service-secrets"
  - name: AWS_ROLE_ARN
    value: "arn:aws:iam::account:role/ChatAIServiceRole"
  - name: AWS_WEB_IDENTITY_TOKEN_FILE
    value: "/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
```

## AWS Secret Structure

### Required Secrets

```json
{
  "LLAMA_CLOUD_API_KEY": "Required for document parsing",
  "OPENROUTER_API_KEY": "Required for AI chat responses"
}
```

### Recommended Secrets

```json
{
  "OPENAI_API_KEY": "Required for semantic search embeddings"
}
```

### Optional Secrets

```json
{
  "QDRANT_API_KEY": "Optional, for Qdrant cloud instances",
  "INTERNAL_API_KEY": "For service-to-service communication",
  "POSTGRES_HOST": "Database hostname",
  "POSTGRES_PORT": "Database port (default: 5432)",
  "POSTGRES_USER": "Database username",
  "POSTGRES_PASSWORD": "Database password",
  "POSTGRES_DB": "Database name",
  "USER_SERVICE_URL": "User service endpoint URL",
  "QDRANT_URL": "Qdrant vector database URL",
  "CHATAI_ORIGIN": "ChatAI service origin URL"
}
```

## IAM Permissions

Your AWS IAM role/user needs these permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:region:account:secret:chatai-service-secrets*"
    }
  ]
}
```

For secret rotation (optional):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue",
        "secretsmanager:DescribeSecret"
      ],
      "Resource": "arn:aws:secretsmanager:region:account:secret:chatai-service-secrets*"
    }
  ]
}
```

## Health Monitoring

### Check AWS Integration Status

```bash
curl http://localhost:3001/health/aws
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "ChatAI AWS Integration",
  "aws": {
    "secretsManager": {
      "service": "AWS Secrets Manager",
      "configured": true,
      "region": "us-east-1",
      "secretName": "chatai-service-secrets",
      "cacheSize": 1,
      "status": "healthy",
      "message": "Successfully connected to AWS Secrets Manager"
    }
  },
  "summary": {
    "usingSecretsManager": true,
    "credentialsAvailable": true,
    "fallbackMode": false
  }
}
```

### Configuration Refresh

The service caches secrets for 5 minutes by default. To force refresh:

1. The cache automatically expires after 5 minutes
2. Service restart will refresh all secrets
3. Configuration includes built-in refresh mechanisms

## Deployment Examples

### Docker

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .

# Set AWS configuration
ENV AWS_REGION=us-east-1
ENV AWS_SECRET_NAME=chatai-service-secrets

EXPOSE 3001
CMD ["npm", "start"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  chatai-service:
    build: .
    ports:
      - "3001:3001"
    environment:
      - AWS_REGION=us-east-1
      - AWS_SECRET_NAME=chatai-service-secrets
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatai-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chatai-service
  template:
    metadata:
      labels:
        app: chatai-service
    spec:
      serviceAccountName: chatai-service-sa
      containers:
      - name: chatai-service
        image: your-registry/chatai-service:latest
        ports:
        - containerPort: 3001
        env:
        - name: AWS_REGION
          value: "us-east-1"
        - name: AWS_SECRET_NAME
          value: "chatai-service-secrets"
        - name: AWS_ROLE_ARN
          value: "arn:aws:iam::account:role/ChatAIServiceRole"
        - name: AWS_WEB_IDENTITY_TOKEN_FILE
          value: "/var/run/secrets/eks.amazonaws.com/serviceaccount/token"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/aws
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Troubleshooting

### Common Issues

1. **AWS credentials not found**
   ```
   ⚠️ AWS credentials not found, will fallback to environment variables
   ```
   Solution: Configure AWS credentials using one of the methods above

2. **Secret not found**
   ```
   ❌ Failed to retrieve secrets from AWS Secrets Manager: ResourceNotFoundException
   ```
   Solution: Create the secret in AWS Secrets Manager with the correct name

3. **Permission denied**
   ```
   ❌ Failed to retrieve secrets from AWS Secrets Manager: AccessDeniedException
   ```
   Solution: Add the required IAM permissions for SecretsManager:GetSecretValue

4. **Invalid region**
   ```
   ❌ Failed to initialize AWS Secrets Manager: InvalidRegionException
   ```
   Solution: Set AWS_REGION environment variable to a valid AWS region

### Debug Mode

Enable detailed logging:

```bash
export LOG_LEVEL=debug
npm start
```

This will show:
- AWS configuration detection
- Secret retrieval attempts
- Cache hits and misses
- Fallback mechanisms

### Fallback Behavior

The service gracefully handles AWS Secrets Manager unavailability:

1. **Primary**: AWS Secrets Manager (if configured)
2. **Fallback**: Environment variables
3. **Default**: Built-in default values (where applicable)

### Health Check Endpoints

- `GET /health` - General service health
- `GET /health/aws` - AWS integration specific health
- `GET /api/v1/chat/health` - API health check

## Security Best Practices

1. **Use IAM roles** instead of access keys when possible
2. **Enable secret rotation** in AWS Secrets Manager
3. **Use least-privilege IAM policies**
4. **Monitor secret access** using CloudTrail
5. **Keep secrets in AWS Secrets Manager** not in environment variables for production
6. **Use different secrets** for different environments (dev/staging/prod)

## Cost Optimization

- Secrets Manager charges per secret per month and per API call
- Local caching reduces API calls (default: 5-minute cache)
- Consider secret consolidation for cost efficiency
- Use environment variables for development to avoid costs

## Migration from Environment Variables

1. Create AWS Secret with current environment variable values
2. Configure AWS credentials and region
3. Set AWS_SECRET_NAME environment variable
4. Restart service (it will automatically use AWS Secrets Manager)
5. Remove environment variable secrets (keep non-secret env vars)

The service automatically detects and uses AWS Secrets Manager when properly configured, with seamless fallback to environment variables. 