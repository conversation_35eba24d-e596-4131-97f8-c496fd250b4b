#!/bin/bash

# Test script for parallel processing optimizations
echo "🚀 Testing Parallel Processing Optimizations"
echo "=============================================="

# Test URL with the same query as before
URL="https://chatai.abstraxn.com/api/v1/chat/?apikey=5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k&query=explain%20policies&stream=true"

echo "📊 Running 3 test requests to measure performance improvements..."
echo ""

# Function to run a single test
run_test() {
    local test_num=$1
    echo "🧪 Test $test_num:"
    echo "=================="
    
    # Record start time
    start_time=$(date +%s.%N)
    
    # Make the curl request with detailed timing
    curl -w "\n⏱️  Performance Metrics:\n   Time to first byte: %{time_starttransfer}s\n   Total time: %{time_total}s\n   DNS lookup: %{time_namelookup}s\n   Connect time: %{time_connect}s\n   HTTP code: %{http_code}\n   Size: %{size_download} bytes\n" \
      -H "Accept: application/json" \
      -H "User-Agent: Parallel-Processing-Test/1.0" \
      -s \
      "$URL" | head -20  # Show only first 20 lines to avoid clutter
    
    # Record end time
    end_time=$(date +%s.%N)
    
    # Calculate duration
    duration=$(echo "$end_time - $start_time" | bc)
    echo "   Script execution time: ${duration}s"
    echo ""
}

# Run 3 tests
for i in {1..3}; do
    run_test $i
    if [ $i -lt 3 ]; then
        echo "⏳ Waiting 2 seconds before next test..."
        sleep 2
        echo ""
    fi
done

echo "✅ Parallel processing tests completed!"
echo ""
echo "🔍 Key metrics to observe:"
echo "   - Time to first byte should be ~0.3-0.5s (session message)"
echo "   - Time to first content should be reduced from ~7s to ~4-5s"
echo "   - Total response time should show improvement"
echo ""
echo "💡 The parallel processing optimizations should reduce the delay"
echo "   between session initialization and first content delivery."
