/**
 * Semantic Refinement Configuration
 * Controls the behavior of the prompt preprocessing system
 */

module.exports = {
  // Main toggle for semantic refinement
  enabled: process.env.SEMANTIC_REFINEMENT_ENABLED !== 'false', // Default: true

  // AI-powered refinement settings
  ai: {
    enabled: process.env.SEMANTIC_AI_ENABLED !== 'false', // Default: true
    model: process.env.SEMANTIC_AI_MODEL || 'openai/gpt-3.5-turbo', // Cheaper model for refinement
    maxTokens: parseInt(process.env.SEMANTIC_AI_MAX_TOKENS) || 150,
    temperature: parseFloat(process.env.SEMANTIC_AI_TEMPERATURE) || 0.3
  },

  // Caching settings
  cache: {
    enabled: process.env.SEMANTIC_CACHE_ENABLED !== 'false', // Default: true
    ttl: parseInt(process.env.SEMANTIC_CACHE_TTL) || 30 * 60 * 1000, // 30 minutes
    maxSize: parseInt(process.env.SEMANTIC_CACHE_MAX_SIZE) || 1000
  },

  // Query analysis thresholds
  analysis: {
    ambiguityThreshold: parseFloat(process.env.SEMANTIC_AMBIGUITY_THRESHOLD) || 0.6,
    completenessThreshold: parseFloat(process.env.SEMANTIC_COMPLETENESS_THRESHOLD) || 0.7,
    complexityThreshold: parseFloat(process.env.SEMANTIC_COMPLEXITY_THRESHOLD) || 0.6,
    confidenceThreshold: parseFloat(process.env.SEMANTIC_CONFIDENCE_THRESHOLD) || 0.5
  },

  // Context enhancement settings
  context: {
    maxHistoryEntries: parseInt(process.env.SEMANTIC_CONTEXT_HISTORY) || 2,
    enablePronounResolution: process.env.SEMANTIC_PRONOUN_RESOLUTION !== 'false',
    enableFollowUpDetection: process.env.SEMANTIC_FOLLOWUP_DETECTION !== 'false',
    enableTopicContinuity: process.env.SEMANTIC_TOPIC_CONTINUITY !== 'false'
  },

  // Query expansion settings
  expansion: {
    maxExpansions: parseInt(process.env.SEMANTIC_MAX_EXPANSIONS) || 5,
    maxQueryLength: parseInt(process.env.SEMANTIC_MAX_QUERY_LENGTH) || 500,
    enableRuleBased: process.env.SEMANTIC_RULE_BASED !== 'false',
    enableSynonyms: process.env.SEMANTIC_SYNONYMS !== 'false',
    enableDomainSpecific: process.env.SEMANTIC_DOMAIN_SPECIFIC !== 'false'
  },

  // Performance settings
  performance: {
    maxProcessingTime: parseInt(process.env.SEMANTIC_MAX_PROCESSING_TIME) || 5000, // 5 seconds
    enableParallelProcessing: process.env.SEMANTIC_PARALLEL_PROCESSING !== 'false',
    enableMetrics: process.env.SEMANTIC_METRICS_ENABLED !== 'false'
  },

  // Logging settings
  logging: {
    enabled: process.env.SEMANTIC_LOGGING_ENABLED !== 'false',
    level: process.env.SEMANTIC_LOG_LEVEL || 'info', // debug, info, warn, error
    logRefinements: process.env.SEMANTIC_LOG_REFINEMENTS !== 'false',
    logAnalysis: process.env.SEMANTIC_LOG_ANALYSIS !== 'false'
  },

  // Domain-specific settings
  domains: {
    // Technical domain
    technical: {
      enabled: true,
      expansionTerms: ['implementation', 'configuration', 'debugging', 'testing', 'deployment'],
      synonyms: {
        'api': ['endpoint', 'service', 'interface'],
        'database': ['db', 'storage', 'repository'],
        'error': ['bug', 'issue', 'exception', 'failure']
      }
    },

    // Business domain
    business: {
      enabled: true,
      expansionTerms: ['requirements', 'stakeholders', 'deliverables', 'metrics', 'roi'],
      synonyms: {
        'revenue': ['income', 'sales', 'earnings'],
        'customer': ['client', 'user', 'consumer'],
        'strategy': ['plan', 'approach', 'methodology']
      }
    },

    // Process domain
    process: {
      enabled: true,
      expansionTerms: ['workflow', 'automation', 'efficiency', 'optimization'],
      synonyms: {
        'workflow': ['process', 'procedure', 'steps'],
        'implementation': ['execution', 'deployment', 'setup']
      }
    },

    // Data domain
    data: {
      enabled: true,
      expansionTerms: ['analytics', 'visualization', 'insights', 'trends', 'reporting'],
      synonyms: {
        'analysis': ['report', 'study', 'evaluation'],
        'metrics': ['measurements', 'statistics', 'kpis']
      }
    }
  },

  // Feature flags for experimental features
  experimental: {
    semanticSimilarity: process.env.SEMANTIC_SIMILARITY_ENABLED === 'true',
    intentPrediction: process.env.SEMANTIC_INTENT_PREDICTION === 'true',
    contextualEmbeddings: process.env.SEMANTIC_CONTEXTUAL_EMBEDDINGS === 'true',
    multiLanguageSupport: process.env.SEMANTIC_MULTILANG_SUPPORT === 'true'
  }
};
