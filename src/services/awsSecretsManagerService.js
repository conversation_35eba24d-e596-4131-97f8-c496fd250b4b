const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');

/**
 * AWS Secrets Manager Service
 * Provides secure secret retrieval with caching and fallback to environment variables
 */
class AwsSecretsManagerService {
  constructor() {
    this.client = null;
    this.secretsCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
    this.isConfigured = false;
    this.region = process.env.AWS_REGION || 'us-east-1';
    this.secretName = process.env.AWS_SECRET_NAME || 'chatai-service-secrets';
    
    this.initializeClient();
  }

  /**
   * Initialize AWS Secrets Manager client
   */
  initializeClient() {
    try {
      // Check if AWS credentials are available
      const hasCredentials = process.env.AWS_ACCESS_KEY_ID || 
                           process.env.AWS_PROFILE || 
                           process.env.AWS_IAM_ROLE_ARN ||
                           process.env.AWS_WEB_IDENTITY_TOKEN_FILE;

      if (!hasCredentials && process.env.NODE_ENV === 'production') {
        console.warn('⚠️ AWS credentials not found in production environment');
        return;
      }

      this.client = new SecretsManagerClient({
        region: this.region,
        // AWS SDK will automatically use credentials from:
        // 1. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
        // 2. AWS credentials file
        // 3. IAM role if running on EC2/ECS/Lambda
        // 4. AWS SSO
      });

      this.isConfigured = true;
      console.log(`✅ AWS Secrets Manager initialized for region: ${this.region}`);
      console.log(`🔐 Secret name: ${this.secretName}`);
    } catch (error) {
      console.error('❌ Failed to initialize AWS Secrets Manager:', error.message);
      console.log('🔄 Will fallback to environment variables');
    }
  }

  /**
   * Get secrets from AWS Secrets Manager
   * @returns {Promise<Object>} Parsed secrets object
   */
  async getSecrets() {
    if (!this.isConfigured) {
      console.log('⚠️ AWS Secrets Manager not configured, using environment variables');
      return this.getEnvironmentVariables();
    }

    // Check cache first
    const cacheKey = this.secretName;
    const cached = this.secretsCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      console.log('📦 Using cached secrets from AWS Secrets Manager');
      return cached.data;
    }

    try {
      console.log(`🔐 Fetching secrets from AWS Secrets Manager: ${this.secretName}`);
      
      const command = new GetSecretValueCommand({
        SecretId: this.secretName,
        VersionStage: 'AWSCURRENT',
      });

      const response = await this.client.send(command);
      
      if (!response.SecretString) {
        throw new Error('Secret string is empty or binary secrets not supported');
      }

      const secrets = JSON.parse(response.SecretString);
      
      // Cache the secrets
      this.secretsCache.set(cacheKey, {
        data: secrets,
        timestamp: Date.now()
      });

      console.log('✅ Successfully retrieved secrets from AWS Secrets Manager');
      console.log(`📊 Retrieved ${Object.keys(secrets).length} secret keys`);
      
      return secrets;
    } catch (error) {
      console.error('❌ Failed to retrieve secrets from AWS Secrets Manager:', error.message);
      console.log('🔄 Falling back to environment variables');
      return this.getEnvironmentVariables();
    }
  }

  /**
   * Get specific secret value
   * @param {string} key - Secret key name
   * @returns {Promise<string|null>} Secret value or null if not found
   */
  async getSecret(key) {
    try {
      const secrets = await this.getSecrets();
      return secrets[key] || null;
    } catch (error) {
      console.error(`❌ Failed to get secret '${key}':`, error.message);
      return null;
    }
  }

  /**
   * Fallback to environment variables when AWS Secrets Manager is not available
   * @returns {Object} Environment variables as secrets object
   */
  getEnvironmentVariables() {
    console.log('📂 Using environment variables as fallback');
    
    return {
      // API Keys
      LLAMA_CLOUD_API_KEY: process.env.LLAMA_CLOUD_API_KEY,
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
      OPENAI_API_KEY: process.env.OPENAI_API_KEY,
      QDRANT_API_KEY: process.env.QDRANT_API_KEY,
      INTERNAL_API_KEY: process.env.INTERNAL_API_KEY,
      
      // Database Credentials
      POSTGRES_HOST: process.env.POSTGRES_HOST,
      POSTGRES_PORT: process.env.POSTGRES_PORT,
      POSTGRES_USER: process.env.POSTGRES_USER,
      POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD,
      POSTGRES_DB: process.env.POSTGRES_DB,
      
      // Service URLs
      USER_SERVICE_URL: process.env.USER_SERVICE_URL,
      QDRANT_URL: process.env.QDRANT_URL,
      CHATAI_ORIGIN: process.env.CHATAI_ORIGIN,
    };
  }

  /**
   * Get database configuration from secrets
   * @returns {Promise<Object>} Database configuration object
   */
  async getDatabaseConfig() {
    const secrets = await this.getSecrets();
    
    return {
      host: secrets.POSTGRES_HOST || 'localhost',
      port: parseInt(secrets.POSTGRES_PORT, 10) || 5432,
      username: secrets.POSTGRES_USER || 'postgres',
      password: secrets.POSTGRES_PASSWORD || 'postgres',
      database: secrets.POSTGRES_DB || 'abstraxn',
    };
  }

  /**
   * Get API keys configuration from secrets
   * @returns {Promise<Object>} API keys configuration object
   */
  async getApiKeysConfig() {
    const secrets = await this.getSecrets();
    
    return {
      llamaCloudApiKey: secrets.LLAMA_CLOUD_API_KEY,
      openRouterApiKey: secrets.OPENROUTER_API_KEY,
      openaiApiKey: secrets.OPENAI_API_KEY,
      qdrantApiKey: secrets.QDRANT_API_KEY,
      internalApiKey: secrets.INTERNAL_API_KEY,
    };
  }

  /**
   * Get service URLs configuration from secrets
   * @returns {Promise<Object>} Service URLs configuration object
   */
  async getServiceUrlsConfig() {
    const secrets = await this.getSecrets();
    
    return {
      userServiceUrl: secrets.USER_SERVICE_URL || 'http://localhost:3000',
      qdrantUrl: secrets.QDRANT_URL || 'http://localhost:6333',
      chatAiOrigin: secrets.CHATAI_ORIGIN || 'http://localhost:3001',
    };
  }

  /**
   * Clear cached secrets (useful for testing or forcing refresh)
   */
  clearCache() {
    this.secretsCache.clear();
    console.log('🗑️ Secrets cache cleared');
  }

  /**
   * Validate that required secrets are available
   * @param {Array<string>} requiredSecrets - Array of required secret keys
   * @returns {Promise<Object>} Validation result with missing secrets
   */
  async validateSecrets(requiredSecrets = []) {
    try {
      const secrets = await this.getSecrets();
      const missing = [];
      const available = [];

      for (const key of requiredSecrets) {
        if (!secrets[key]) {
          missing.push(key);
        } else {
          available.push(key);
        }
      }

      return {
        isValid: missing.length === 0,
        missing,
        available,
        total: requiredSecrets.length
      };
    } catch (error) {
      console.error('❌ Failed to validate secrets:', error.message);
      return {
        isValid: false,
        missing: requiredSecrets,
        available: [],
        total: requiredSecrets.length,
        error: error.message
      };
    }
  }

  /**
   * Health check for AWS Secrets Manager service
   * @returns {Promise<Object>} Health check result
   */
  async healthCheck() {
    const result = {
      service: 'AWS Secrets Manager',
      configured: this.isConfigured,
      region: this.region,
      secretName: this.secretName,
      cacheSize: this.secretsCache.size,
      status: 'unknown'
    };

    if (!this.isConfigured) {
      result.status = 'fallback';
      result.message = 'Using environment variables';
      return result;
    }

    try {
      await this.getSecrets();
      result.status = 'healthy';
      result.message = 'Successfully connected to AWS Secrets Manager';
    } catch (error) {
      result.status = 'error';
      result.message = error.message;
    }

    return result;
  }
}

// Export singleton instance
module.exports = new AwsSecretsManagerService(); 