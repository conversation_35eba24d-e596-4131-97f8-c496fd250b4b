# Server Configuration
PORT=3001
NODE_ENV=development

# User Service Configuration
USER_SERVICE_URL=http://localhost:3000

# ChatAI Origin for API Key Validation
# This should match the CHATAI_ORIGIN environment variable in User-Service
CHATAI_ORIGIN=http://localhost:3001

# LlamaIndex Cloud Configuration
LLAMA_CLOUD_API_KEY=key_here

# OpenRouter Configuration (for chat responses)
OPENROUTER_API_KEY=key_here

OPENAI_API_KEY=your_openai_api_key_here

# Qdrant Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_secure_api_key_here
QDRANT_COLLECTION=chatai_documents


# Cache Configuration
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
CLEANUP_INTERVAL_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
ENABLE_DETAILED_FILTERING_LOGS=true
MAX_LOG_FILE_SIZE=10485760

# Smart Chunking Configuration
SMART_CHUNKING_ENABLED=true
TARGET_CHUNK_SIZE=1000
MIN_CHUNK_SIZE=500
MAX_CHUNK_SIZE=1500
CHUNK_OVERLAP_PERCENT=0.1
